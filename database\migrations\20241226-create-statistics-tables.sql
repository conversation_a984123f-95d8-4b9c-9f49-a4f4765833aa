-- 创建问卷统计表
CREATE TABLE IF NOT EXISTS `questionnaire_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `questionnaire_id` int(11) NOT NULL COMMENT '问卷ID',
  `sso_school_code` varchar(50) NOT NULL COMMENT 'SSO学校编码',
  `month` varchar(7) NOT NULL COMMENT '统计月份 YYYY-MM',
  `total_students` int(11) NOT NULL DEFAULT 0 COMMENT '学生总数',
  `submitted_count` int(11) NOT NULL DEFAULT 0 COMMENT '已提交问卷数量',
  `incomplete_count` int(11) NOT NULL DEFAULT 0 COMMENT '未提交问卷数量',
  `completion_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '完成率百分比',
  `school_average_score` decimal(5,2) DEFAULT NULL COMMENT '学校平均评分',
  `teacher_average_score` decimal(5,2) DEFAULT NULL COMMENT '教师平均评分',
  `total_teachers` int(11) NOT NULL DEFAULT 0 COMMENT '参与评价的教师总数',
  `grade_statistics` text COMMENT '按年级分组的统计数据(JSON格式)',
  `class_statistics` text COMMENT '按班级分组的统计数据(JSON格式)',
  `teacher_ranking` text COMMENT '教师排名数据(JSON格式)',
  `status` enum('pending','calculating','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '统计状态',
  `last_calculated_at` datetime DEFAULT NULL COMMENT '最后计算时间',
  `calculation_duration` int(11) NOT NULL DEFAULT 0 COMMENT '计算耗时(毫秒)',
  `error_message` text COMMENT '错误信息',
  `triggered_by` varchar(50) DEFAULT NULL COMMENT '触发统计的用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_questionnaire_statistics_unique` (`questionnaire_id`,`sso_school_code`,`month`),
  KEY `idx_questionnaire_statistics_questionnaire_id` (`questionnaire_id`),
  KEY `idx_questionnaire_statistics_school_code` (`sso_school_code`),
  KEY `idx_questionnaire_statistics_month` (`month`),
  KEY `idx_questionnaire_statistics_calculated_at` (`last_calculated_at`),
  CONSTRAINT `fk_questionnaire_statistics_questionnaire` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaires` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问卷统计表';

-- 创建未填写学生缓存表
CREATE TABLE IF NOT EXISTS `incomplete_students_cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '缓存ID',
  `statistics_id` int(11) NOT NULL COMMENT '统计ID',
  `sso_student_code` varchar(50) NOT NULL COMMENT '学生编码',
  `sso_student_name` varchar(100) NOT NULL COMMENT '学生姓名',
  `grade_code` varchar(20) NOT NULL COMMENT '年级编码',
  `grade_name` varchar(100) NOT NULL COMMENT '年级名称',
  `class_code` varchar(20) NOT NULL COMMENT '班级编码',
  `class_name` varchar(100) NOT NULL COMMENT '班级名称',
  `student_mobile` varchar(20) DEFAULT NULL COMMENT '学生手机号',
  `parent_contacts` varchar(200) DEFAULT NULL COMMENT '家长联系方式(JSON格式)',
  `student_status` varchar(10) DEFAULT NULL COMMENT '学生状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_incomplete_students_statistics_id` (`statistics_id`),
  KEY `idx_incomplete_students_grade_code` (`grade_code`),
  KEY `idx_incomplete_students_class_code` (`class_code`),
  KEY `idx_incomplete_students_grade_class` (`grade_code`,`class_code`),
  KEY `idx_incomplete_students_student_code` (`sso_student_code`),
  CONSTRAINT `fk_incomplete_students_cache_statistics` FOREIGN KEY (`statistics_id`) REFERENCES `questionnaire_statistics` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='未填写学生缓存表';
