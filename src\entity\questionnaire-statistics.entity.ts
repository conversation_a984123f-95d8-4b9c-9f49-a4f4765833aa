import {
  Column,
  Model,
  Table,
  DataType,
  CreatedAt,
  UpdatedAt,
  Index,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Questionnaire } from './questionnaire.entity';

/**
 * 问卷统计表
 * 用于缓存问卷的统计数据，避免实时计算带来的性能问题
 */
@Table({
  tableName: 'questionnaire_statistics',
  comment: '问卷统计表',
  indexes: [
    {
      fields: ['questionnaire_id'],
      name: 'idx_questionnaire_statistics_questionnaire_id',
    },
    {
      fields: ['sso_school_code'],
      name: 'idx_questionnaire_statistics_school_code',
    },
    {
      fields: ['month'],
      name: 'idx_questionnaire_statistics_month',
    },
    {
      fields: ['questionnaire_id', 'sso_school_code', 'month'],
      unique: true,
      name: 'uk_questionnaire_statistics_unique',
    },
    {
      fields: ['last_calculated_at'],
      name: 'idx_questionnaire_statistics_calculated_at',
    },
  ],
})
export class QuestionnaireStatistics extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '统计ID',
  })
  id: number;

  @ForeignKey(() => Questionnaire)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '问卷ID',
  })
  questionnaire_id: number;

  @BelongsTo(() => Questionnaire)
  questionnaire: Questionnaire;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: 'SSO学校编码',
  })
  sso_school_code: string;

  @Column({
    type: DataType.STRING(7),
    allowNull: false,
    comment: '统计月份 YYYY-MM',
  })
  month: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '学生总数',
  })
  total_students: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '已提交问卷数量',
  })
  submitted_count: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '未提交问卷数量',
  })
  incomplete_count: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0,
    comment: '完成率百分比',
  })
  completion_rate: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true,
    comment: '学校平均评分',
  })
  school_average_score: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true,
    comment: '教师平均评分',
  })
  teacher_average_score: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '参与评价的教师总数',
  })
  total_teachers: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '按年级分组的统计数据(JSON格式)',
  })
  grade_statistics: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '按班级分组的统计数据(JSON格式)',
  })
  class_statistics: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '教师排名数据(JSON格式)',
  })
  teacher_ranking: string;

  @Column({
    type: DataType.ENUM('pending', 'calculating', 'completed', 'failed'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '统计状态',
  })
  status: 'pending' | 'calculating' | 'completed' | 'failed';

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '最后计算时间',
  })
  last_calculated_at: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '计算耗时(毫秒)',
  })
  calculation_duration: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '错误信息',
  })
  error_message: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '触发统计的用户ID',
  })
  triggered_by: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    comment: '创建时间',
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    comment: '更新时间',
  })
  updated_at: Date;
}
