import { createApp, close } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('Statistics Grade Class Code Fix Test', () => {
  let app: Application;
  let dbConnected = false;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
      dbConnected = true;
      console.log('✅ 数据库连接成功，运行完整测试');
    } catch (err) {
      console.warn('⚠️ 数据库连接失败，跳过需要数据库的测试:', err.message);
      dbConnected = false;
    }
  }, 15000);

  afterAll(async () => {
    if (app) {
      await close(app);
    }
  });

  describe('年级班级编码一致性测试', () => {
    it('应该验证统计服务和响应服务使用相同的年级班级编码提取逻辑', async () => {
      if (!dbConnected) {
        console.log('⏭️ 跳过测试：数据库未连接');
        return;
      }

      // 模拟学生数据
      const mockStudentData = {
        code: 'test_student_001',
        name: '张小明',
        classes: [
          {
            name: '5班',
            grade_name: '四年级',
            code: 'class_original_code_5',
            grade_code: 'grade_original_code_4',
          }
        ]
      };

      try {
        // 获取StatisticsService实例
        const statisticsService = await app.getApplicationContext().getAsync('statisticsService');
        
        // 使用反射调用私有方法测试年级班级编码提取
        const extractMethod = (statisticsService as any).extractGradeAndClassCodes.bind(statisticsService);
        const result = extractMethod(mockStudentData);

        console.log('统计服务年级班级编码提取测试:', {
          input: {
            student_code: mockStudentData.code,
            grade_name: mockStudentData.classes[0].grade_name,
            class_name: mockStudentData.classes[0].name,
            original_grade_code: mockStudentData.classes[0].grade_code,
            original_class_code: mockStudentData.classes[0].code,
          },
          output: result,
        });

        // 验证提取结果
        expect(result.gradeCode).toBe('4'); // 从"四年级"提取出"4"
        expect(result.classCode).toBe('5'); // 从"5班"提取出"5"

        console.log('✅ 统计服务年级班级编码提取正常');
      } catch (error) {
        console.error('❌ 统计服务年级班级编码提取测试失败:', error);
        throw error;
      }
    });

    it('应该验证不同格式的年级班级信息提取一致性', async () => {
      if (!dbConnected) {
        console.log('⏭️ 跳过测试：数据库未连接');
        return;
      }

      const testCases = [
        {
          input: { grade_name: '四年级', class_name: '5班' },
          expected: { gradeCode: '4', classCode: '5' },
        },
        {
          input: { grade_name: '初二', class_name: '3班' },
          expected: { gradeCode: '8', classCode: '3' },
        },
        {
          input: { grade_name: '高中一年级', class_name: 'A班' },
          expected: { gradeCode: '10', classCode: '1' },
        },
      ];

      try {
        const statisticsService = await app.getApplicationContext().getAsync('statisticsService');
        const extractMethod = (statisticsService as any).extractGradeAndClassCodes.bind(statisticsService);

        testCases.forEach(({ input, expected }, index) => {
          const mockStudent = {
            code: `test_student_${index + 1}`,
            name: `测试学生${index + 1}`,
            classes: [
              {
                name: input.class_name,
                grade_name: input.grade_name,
                code: `original_class_${index + 1}`,
                grade_code: `original_grade_${index + 1}`,
              }
            ]
          };

          const result = extractMethod(mockStudent);

          console.log(`测试用例 ${index + 1}:`, {
            input,
            output: result,
            expected,
          });

          expect(result.gradeCode).toBe(expected.gradeCode);
          expect(result.classCode).toBe(expected.classCode);
        });

        console.log('✅ 多种格式年级班级编码提取一致性验证通过');
      } catch (error) {
        console.error('❌ 年级班级编码提取一致性测试失败:', error);
        throw error;
      }
    });
  });

  describe('未填写学生统计修复验证', () => {
    it('应该能正确识别已填写的学生', async () => {
      if (!dbConnected) {
        console.log('⏭️ 跳过测试：数据库未连接');
        return;
      }

      // 这个测试需要真实的数据库数据，主要用于验证修复效果
      console.log('📝 提示：此测试需要真实数据验证');
      console.log('请在有真实数据的环境中运行以验证修复效果');
      console.log('验证步骤：');
      console.log('1. 确认responses表中有4年级5班的学生记录');
      console.log('2. 调用未填写学生统计API');
      console.log('3. 验证4年级5班的学生不会出现在未填写列表中');
    });
  });

  describe('边界情况测试', () => {
    it('应该处理没有班级信息的学生', async () => {
      if (!dbConnected) {
        console.log('⏭️ 跳过测试：数据库未连接');
        return;
      }

      const mockStudentWithoutClass = {
        code: 'test_student_no_class',
        name: '无班级学生',
        classes: []
      };

      try {
        const statisticsService = await app.getApplicationContext().getAsync('statisticsService');
        const extractMethod = (statisticsService as any).extractGradeAndClassCodes.bind(statisticsService);
        
        const result = extractMethod(mockStudentWithoutClass);

        console.log('无班级信息学生测试:', {
          input: mockStudentWithoutClass,
          output: result,
        });

        expect(result.gradeCode).toBeNull();
        expect(result.classCode).toBeNull();

        console.log('✅ 无班级信息边界情况处理正常');
      } catch (error) {
        console.error('❌ 边界情况测试失败:', error);
        throw error;
      }
    });
  });
});
