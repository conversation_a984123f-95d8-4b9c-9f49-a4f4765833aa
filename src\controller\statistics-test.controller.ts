import { Controller, Post, Get, Inject, Query, Param } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { StatisticsTaskSimpleService } from '../service/statistics-task-simple.service';

/**
 * 统计测试控制器（公开访问，无需认证）
 */
@Controller('/public/statistics-test')
export class StatisticsTestController {
  @Inject()
  statisticsTaskService: StatisticsTaskSimpleService;

  @Inject()
  ctx: Context;

  /**
   * 测试触发统计计算
   */
  @Post('/trigger/:questionnaireId')
  async testTriggerStatistics(
    @Param('questionnaireId') questionnaireId: number
  ) {
    try {
      this.ctx.logger.info('测试触发统计计算请求', {
        questionnaire_id: questionnaireId,
      });

      const statistics =
        await this.statisticsTaskService.triggerStatisticsCalculation(
          questionnaireId,
          'test_user'
        );

      return {
        errCode: 0,
        msg: '统计任务已启动',
        data: statistics,
      };
    } catch (error) {
      this.ctx.logger.error('测试触发统计计算失败', {
        questionnaire_id: questionnaireId,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '触发统计计算失败',
        data: null,
      };
    }
  }

  /**
   * 测试获取统计状态
   */
  @Get('/status/:questionnaireId')
  async testGetStatisticsStatus(
    @Param('questionnaireId') questionnaireId: number
  ) {
    try {
      this.ctx.logger.info('测试获取统计状态请求', {
        questionnaire_id: questionnaireId,
      });

      const statistics = await this.statisticsTaskService.getStatisticsStatus(
        questionnaireId
      );

      return {
        errCode: 0,
        msg: '获取统计状态成功',
        data: statistics,
      };
    } catch (error) {
      this.ctx.logger.error('测试获取统计状态失败', {
        questionnaire_id: questionnaireId,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '获取统计状态失败',
        data: null,
      };
    }
  }

  /**
   * 测试获取缓存的统计数据
   */
  @Get('/cached/:questionnaireId')
  async testGetCachedStatistics(
    @Param('questionnaireId') questionnaireId: number
  ) {
    try {
      this.ctx.logger.info('测试获取缓存统计数据请求', {
        questionnaire_id: questionnaireId,
      });

      const cachedData = await this.statisticsTaskService.getCachedStatistics(
        questionnaireId
      );

      return {
        errCode: 0,
        msg: '获取缓存统计数据成功',
        data: cachedData,
      };
    } catch (error) {
      this.ctx.logger.error('测试获取缓存统计数据失败', {
        questionnaire_id: questionnaireId,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '获取缓存统计数据失败',
        data: null,
      };
    }
  }

  /**
   * 测试获取未填写学生列表
   */
  @Get('/incomplete-students/:questionnaireId')
  async testGetIncompleteStudents(
    @Param('questionnaireId') questionnaireId: number,
    @Query('grade_code') gradeCode?: string,
    @Query('class_code') classCode?: string,
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number
  ) {
    try {
      this.ctx.logger.info('测试获取未填写学生列表请求', {
        questionnaire_id: questionnaireId,
        grade_code: gradeCode,
        class_code: classCode,
        page,
        pageSize,
      });

      const result =
        await this.statisticsTaskService.getCachedIncompleteStudents(
          questionnaireId,
          gradeCode,
          classCode,
          page || 1,
          pageSize || 20
        );

      return {
        errCode: 0,
        msg: '获取未填写学生列表成功',
        data: result,
      };
    } catch (error) {
      this.ctx.logger.error('测试获取未填写学生列表失败', {
        questionnaire_id: questionnaireId,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '获取未填写学生列表失败',
        data: null,
      };
    }
  }

  /**
   * 测试API概览
   */
  @Get('/info')
  async getTestInfo() {
    return {
      errCode: 0,
      msg: '统计任务测试API',
      data: {
        description: '这是统计任务功能的测试API，无需认证即可访问',
        endpoints: [
          {
            method: 'POST',
            path: '/public/statistics-test/trigger/{questionnaireId}',
            description: '触发统计计算',
          },
          {
            method: 'GET',
            path: '/public/statistics-test/status/{questionnaireId}',
            description: '获取统计状态',
          },
          {
            method: 'GET',
            path: '/public/statistics-test/cached/{questionnaireId}',
            description: '获取缓存的统计数据',
          },
          {
            method: 'GET',
            path: '/public/statistics-test/incomplete-students/{questionnaireId}',
            description: '获取未填写学生列表',
            query_params: ['grade_code', 'class_code', 'page', 'pageSize'],
          },
        ],
        version: '1.0.0',
        timestamp: new Date(),
      },
    };
  }
}
