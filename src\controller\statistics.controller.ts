import { Controller, Get, Inject, Query, Param } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { StatisticsService } from '../service/statistics.service';
import {
  SchoolStatisticsQueryDTO,
  TeacherStatisticsQueryDTO,
  TeacherRankingQueryDTO,
  TrendAnalysisQueryDTO,
  IncompleteStudentsQueryDTO,
} from '../dto/statistics.dto';
import { ErrorCode } from '../common/ErrorCode';

@Controller('/api/statistics')
export class StatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  statisticsService: StatisticsService;

  /**
   * 获取学校维度统计
   */
  @Get('/school')
  @Validate()
  async getSchoolStatistics(@Query() queryDto: SchoolStatisticsQueryDTO) {
    try {
      this.ctx.logger.info('获取学校统计数据请求', {
        sso_school_code: queryDto.sso_school_code,
        month: queryDto.month,
        start_month: queryDto.start_month,
        end_month: queryDto.end_month,
      });

      const statistics = await this.statisticsService.getSchoolStatistics(
        queryDto
      );

      return statistics;
    } catch (error) {
      this.ctx.logger.error('获取学校统计数据失败', error, {
        sso_school_code: queryDto.sso_school_code,
      });

      throw error;
    }
  }

  /**
   * 获取教师维度统计
   */
  @Get('/teacher')
  @Validate()
  async getTeacherStatistics(@Query() queryDto: TeacherStatisticsQueryDTO) {
    try {
      this.ctx.logger.info('获取教师统计数据请求', {
        sso_teacher_id: queryDto.sso_teacher_id,
        sso_school_code: queryDto.sso_school_code,
        month: queryDto.month,
      });

      const statistics = await this.statisticsService.getTeacherStatistics(
        queryDto
      );

      return statistics;
    } catch (error) {
      this.ctx.logger.error('获取教师统计数据失败', error, {
        sso_teacher_id: queryDto.sso_teacher_id,
      });

      throw error;
    }
  }

  /**
   * 获取教师排名
   */
  @Get('/teacher-ranking')
  @Validate()
  async getTeacherRanking(@Query() queryDto: TeacherRankingQueryDTO) {
    try {
      this.ctx.logger.info('获取教师排名请求', {
        sso_school_code: queryDto.sso_school_code,
        month: queryDto.month,
        subject: queryDto.subject,
        department: queryDto.department,
        sort_by: queryDto.sort_by,
      });

      const ranking = await this.statisticsService.getTeacherRanking(queryDto);

      return ranking;
    } catch (error) {
      this.ctx.logger.error('获取教师排名失败', error, {
        sso_school_code: queryDto.sso_school_code,
      });

      throw error;
    }
  }

  /**
   * 获取趋势分析数据
   */
  @Get('/trend')
  @Validate()
  async getTrendAnalysis(@Query() queryDto: TrendAnalysisQueryDTO) {
    try {
      this.ctx.logger.info('获取趋势分析数据请求', {
        sso_school_code: queryDto.sso_school_code,
        start_month: queryDto.start_month,
        end_month: queryDto.end_month,
        analysis_type: queryDto.analysis_type,
      });

      const trendData = await this.statisticsService.getTrendAnalysis(queryDto);

      return trendData;
    } catch (error) {
      this.ctx.logger.error('获取趋势分析数据失败', error, {
        sso_school_code: queryDto.sso_school_code,
      });

      throw error;
    }
  }

  /**
   * 获取未填写学生名单（分页）
   */
  @Get('/incomplete-students')
  @Validate()
  async getIncompleteStudents(@Query() queryDto: IncompleteStudentsQueryDTO) {
    try {
      this.ctx.logger.info('获取未填写学生名单请求', {
        sso_school_code: queryDto.sso_school_code,
        questionnaire_id: queryDto.questionnaire_id,
        month: queryDto.month,
        page: queryDto.page,
        pageSize: queryDto.pageSize,
        grade_code: queryDto.grade_code,
        class_code: queryDto.class_code,
      });

      const incompleteStudents =
        await this.statisticsService.getIncompleteStudentsWithPagination(
          queryDto.sso_school_code,
          queryDto.questionnaire_id,
          queryDto.month,
          queryDto.page || 1,
          queryDto.pageSize || 20,
          queryDto.grade_code,
          queryDto.class_code
        );

      return incompleteStudents;
    } catch (error) {
      this.ctx.logger.error('获取未填写学生名单失败', error, {
        sso_school_code: queryDto.sso_school_code,
      });

      throw error;
    }
  }

  /**
   * 获取教师评分分布
   */
  @Get('/teacher/:teacherId/distribution')
  async getTeacherScoreDistribution(
    @Param('teacherId') teacherId: string,
    @Query() query: { sso_school_code?: string; month?: string }
  ) {
    try {
      const { sso_school_code, month } = query;

      this.ctx.logger.info('获取教师评分分布请求', {
        sso_teacher_id: teacherId,
        sso_school_code,
        month,
      });

      const distribution = await this.statisticsService.getScoreDistribution(
        teacherId,
        sso_school_code,
        month
      );

      return distribution;
    } catch (error) {
      this.ctx.logger.error('获取教师评分分布失败', error, {
        sso_teacher_id: teacherId,
      });

      throw error;
    }
  }

  /**
   * 获取教师关键词云
   */
  @Get('/teacher/:teacherId/keywords')
  async getTeacherKeywords(
    @Param('teacherId') teacherId: string,
    @Query() query: { sso_school_code?: string; month?: string }
  ) {
    try {
      const { sso_school_code, month } = query;

      this.ctx.logger.info('获取教师关键词云请求', {
        sso_teacher_id: teacherId,
        sso_school_code,
        month,
      });

      const keywords = await this.statisticsService.getKeywordCloud(
        teacherId,
        sso_school_code,
        month
      );

      return keywords;
    } catch (error) {
      this.ctx.logger.error('获取教师关键词云失败', error, {
        sso_teacher_id: teacherId,
      });

      throw error;
    }
  }

  /**
   * 获取学校响应趋势
   */
  @Get('/school/:schoolId/trend')
  async getSchoolTrend(
    @Param('schoolId') schoolId: string,
    @Query() query: { start_month: string; end_month: string }
  ) {
    try {
      const { start_month, end_month } = query;

      if (!start_month || !end_month) {
        return {
          errCode: ErrorCode.PARAM_ERROR,
          msg: '开始月份和结束月份是必填参数',
          data: null,
        };
      }

      this.ctx.logger.info('获取学校响应趋势请求', {
        sso_school_code: schoolId,
        start_month,
        end_month,
      });

      const trendData = await this.statisticsService.getResponseTrend(
        schoolId,
        start_month,
        end_month
      );

      return trendData;
    } catch (error) {
      this.ctx.logger.error('获取学校响应趋势失败', error, {
        sso_school_code: schoolId,
      });

      throw error;
    }
  }

  /**
   * 获取教师评价趋势
   */
  @Get('/teacher/:teacherId/trend')
  async getTeacherTrend(
    @Param('teacherId') teacherId: string,
    @Query()
    query: {
      sso_school_code?: string;
      start_month?: string;
      end_month?: string;
    }
  ) {
    try {
      const { sso_school_code, start_month, end_month } = query;

      this.ctx.logger.info('获取教师评价趋势请求', {
        sso_teacher_id: teacherId,
        sso_school_code,
        start_month,
        end_month,
      });

      const trendData = await this.statisticsService.getTeacherTrend(
        teacherId,
        sso_school_code,
        start_month,
        end_month
      );

      return trendData;
    } catch (error) {
      this.ctx.logger.error('获取教师评价趋势失败', error, {
        sso_teacher_id: teacherId,
      });

      throw error;
    }
  }
}
