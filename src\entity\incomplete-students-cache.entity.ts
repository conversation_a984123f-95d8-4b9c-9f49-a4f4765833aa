import {
  Column,
  Model,
  Table,
  DataType,
  CreatedAt,
  UpdatedAt,
  Index,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { QuestionnaireStatistics } from './questionnaire-statistics.entity';

/**
 * 未填写学生缓存表
 * 用于缓存未填写问卷的学生详细信息
 */
@Table({
  tableName: 'incomplete_students_cache',
  comment: '未填写学生缓存表',
  indexes: [
    {
      fields: ['statistics_id'],
      name: 'idx_incomplete_students_statistics_id',
    },
    {
      fields: ['grade_code'],
      name: 'idx_incomplete_students_grade_code',
    },
    {
      fields: ['class_code'],
      name: 'idx_incomplete_students_class_code',
    },
    {
      fields: ['grade_code', 'class_code'],
      name: 'idx_incomplete_students_grade_class',
    },
    {
      fields: ['sso_student_code'],
      name: 'idx_incomplete_students_student_code',
    },
  ],
})
export class IncompleteStudentsCache extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '缓存ID',
  })
  id: number;

  @ForeignKey(() => QuestionnaireStatistics)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '统计ID',
  })
  statistics_id: number;

  @BelongsTo(() => QuestionnaireStatistics)
  statistics: QuestionnaireStatistics;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学生编码',
  })
  sso_student_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '学生姓名',
  })
  sso_student_name: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '年级编码',
  })
  grade_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '年级名称',
  })
  grade_name: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '班级编码',
  })
  class_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '班级名称',
  })
  class_name: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学生手机号',
  })
  student_mobile: string;

  @Column({
    type: DataType.STRING(200),
    allowNull: true,
    comment: '家长联系方式(JSON格式)',
  })
  parent_contacts: string;

  @Column({
    type: DataType.STRING(10),
    allowNull: true,
    comment: '学生状态',
  })
  student_status: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    comment: '创建时间',
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    comment: '更新时间',
  })
  updated_at: Date;
}
