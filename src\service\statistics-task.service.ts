import { Inject, Provide } from '@midwayjs/core';
import { InjectRepository, InjectDataSource } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { CustomError } from '../error/custom.error';
import { QuestionnaireStatistics } from '../entity/questionnaire-statistics.entity';
import { IncompleteStudentsCache } from '../entity/incomplete-students-cache.entity';
import { Questionnaire } from '../entity/questionnaire.entity';
import { Response } from '../entity/response.entity';
import { Answer } from '../entity/answer.entity';
import { Custome } from './api_sso/custome.service';
import { Context } from '@midwayjs/koa';

/**
 * 统计任务服务
 * 负责执行问卷统计计算和缓存管理
 */
@Provide()
export class StatisticsTaskService {
  @InjectRepository(QuestionnaireStatistics)
  statisticsRepository: Repository<QuestionnaireStatistics>;

  @InjectRepository(IncompleteStudentsCache)
  incompleteStudentsCacheRepository: Repository<IncompleteStudentsCache>;

  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @InjectRepository(Response)
  responseRepository: Repository<Response>;

  @InjectRepository(Answer)
  answerRepository: Repository<Answer>;

  @InjectDataSource()
  sequelize: Sequelize;

  @Inject()
  customeService: Custome;

  @Inject()
  ctx: Context;

  /**
   * 触发问卷统计计算
   * @param questionnaireId 问卷ID
   * @param triggeredBy 触发用户ID
   * @returns 统计任务信息
   */
  async triggerStatisticsCalculation(
    questionnaireId: number,
    triggeredBy: string
  ): Promise<QuestionnaireStatistics> {
    // 1. 验证问卷是否存在
    const questionnaire = await this.questionnaireRepository.findByPk(
      questionnaireId
    );
    if (!questionnaire) {
      throw new CustomError('问卷不存在');
    }

    // 2. 检查是否已有统计记录
    let statistics = await this.statisticsRepository.findOne({
      where: {
        questionnaire_id: questionnaireId,
        sso_school_code: questionnaire.sso_school_code,
        month: questionnaire.month,
      },
    });

    // 3. 如果没有统计记录，创建新的
    if (!statistics) {
      statistics = await this.statisticsRepository.create({
        questionnaire_id: questionnaireId,
        sso_school_code: questionnaire.sso_school_code,
        month: questionnaire.month,
        status: 'pending',
        triggered_by: triggeredBy,
      });
    } else {
      // 4. 如果已有记录，检查是否正在计算中
      if (statistics.status === 'calculating') {
        throw new CustomError('统计正在计算中，请稍后再试');
      }

      // 5. 更新触发信息
      await statistics.update({
        status: 'pending',
        triggered_by: triggeredBy,
        error_message: null,
      });
    }

    // 6. 异步执行统计计算
    this.executeStatisticsCalculation(statistics.id).catch(error => {
      console.error('统计计算异步执行失败:', error);
    });

    return statistics;
  }

  /**
   * 执行统计计算
   * @param statisticsId 统计ID
   */
  private async executeStatisticsCalculation(
    statisticsId: number
  ): Promise<void> {
    const startTime = Date.now();
    let statistics: QuestionnaireStatistics;

    try {
      // 1. 获取统计记录
      statistics = await this.statisticsRepository.findByPk(statisticsId, {
        include: [{ model: Questionnaire, as: 'questionnaire' }],
      });

      if (!statistics) {
        throw new Error('统计记录不存在');
      }

      // 2. 更新状态为计算中
      await statistics.update({
        status: 'calculating',
        last_calculated_at: new Date(),
      });

      console.log('开始执行统计计算', {
        statistics_id: statisticsId,
        questionnaire_id: statistics.questionnaire_id,
        school_code: statistics.sso_school_code,
        month: statistics.month,
      });

      // 3. 执行统计计算
      const calculationResult = await this.calculateStatistics(statistics);

      // 4. 保存统计结果
      await this.saveStatisticsResult(statistics, calculationResult);

      // 5. 更新状态为完成
      const duration = Date.now() - startTime;
      await statistics.update({
        status: 'completed',
        calculation_duration: duration,
        error_message: null,
      });

      console.log('统计计算完成', {
        statistics_id: statisticsId,
        duration: `${duration}ms`,
        total_students: calculationResult.totalStudents,
        submitted_count: calculationResult.submittedCount,
        completion_rate: calculationResult.completionRate,
      });
    } catch (error) {
      console.error('统计计算失败', {
        statistics_id: statisticsId,
        error: error.message,
        stack: error.stack,
      });

      // 更新状态为失败
      if (statistics) {
        const duration = Date.now() - startTime;
        await statistics.update({
          status: 'failed',
          calculation_duration: duration,
          error_message: error.message,
        });
      }

      throw error;
    }
  }

  /**
   * 计算统计数据
   * @param statistics 统计记录
   * @returns 计算结果
   */
  private async calculateStatistics(
    statistics: QuestionnaireStatistics
  ): Promise<any> {
    console.log('执行统计计算逻辑', {
      questionnaire_id: statistics.questionnaire_id,
      school_code: statistics.sso_school_code,
      month: statistics.month,
    });

    // 1. 获取学校所有学生
    const allStudents = await this.customeService.getStudents({
      enterpriseCode: statistics.sso_school_code,
    });

    console.log('获取学校学生数据', {
      total_students: allStudents.length,
    });

    // 2. 获取已提交的问卷响应
    const submittedResponses = await this.responseRepository.findAll({
      where: {
        questionnaire_id: statistics.questionnaire_id,
        month: statistics.month,
        is_completed: true,
      },
      include: [
        {
          model: Answer,
          as: 'answers',
        },
      ],
    });

    console.log('获取已提交问卷数据', {
      submitted_count: submittedResponses.length,
    });

    // 3. 计算基础统计数据
    const totalStudents = allStudents.length;
    console.log('totalStudents----', totalStudents);

    const completedStudentCodes = new Set(
      submittedResponses.map(r => r.sso_student_code)
    );
    const completedStudents = completedStudentCodes.size;
    console.log('completedStudents----', completedStudents);
    const submittedCount = submittedResponses.length; // 保留响应总数用于其他统计
    const incompleteCount = totalStudents - completedStudents;

    const completionRate =
      totalStudents > 0
        ? Math.round((completedStudents / totalStudents) * 10000) / 100
        : 0;

    // 4. 计算平均分
    let schoolAverageScore = 0;
    let teacherAverageScore = 0;
    let totalTeachers = 0;

    if (submittedResponses.length > 0) {
      // 学校平均分
      const schoolScores = submittedResponses
        .filter(r => r.school_rating !== null)
        .map(r => r.school_rating);

      if (schoolScores.length > 0) {
        schoolAverageScore =
          Math.round(
            (schoolScores.reduce((sum, score) => sum + score, 0) /
              schoolScores.length) *
              100
          ) / 100;
      }

      // 教师平均分 - 使用兼容的方式替代flatMap
      const allAnswers: any[] = [];
      submittedResponses.forEach((r: any) => {
        if (r.answers && Array.isArray(r.answers)) {
          allAnswers.push(...r.answers);
        }
      });

      if (allAnswers.length > 0) {
        teacherAverageScore =
          Math.round(
            (allAnswers.reduce(
              (sum: number, answer: any) => sum + answer.rating,
              0
            ) /
              allAnswers.length) *
              100
          ) / 100;
        totalTeachers = new Set(allAnswers.map((a: any) => a.sso_teacher_id))
          .size;
      }
    }

    // 5. 计算年级和班级统计
    const { gradeStatistics, classStatistics, incompleteStudents } =
      await this.calculateGradeClassStatistics(
        allStudents,
        submittedResponses,
        statistics
      );

    // 6. 计算教师排名
    const teacherRanking = this.calculateTeacherRanking(submittedResponses);

    return {
      totalStudents,
      submittedCount, // 响应总数
      completedStudents, // 已完成学生数（用于完成率计算）
      incompleteCount,
      completionRate,
      schoolAverageScore,
      teacherAverageScore,
      totalTeachers,
      gradeStatistics,
      classStatistics,
      teacherRanking,
      incompleteStudents,
    };
  }

  /**
   * 保存统计结果
   * @param statistics 统计记录
   * @param result 计算结果
   */
  private async saveStatisticsResult(
    statistics: QuestionnaireStatistics,
    result: any
  ): Promise<void> {
    // 使用事务保存统计结果
    const transaction: Transaction =
      await this.statisticsRepository.sequelize.transaction();

    try {
      // 1. 更新统计主表
      await statistics.update(
        {
          total_students: result.totalStudents,
          submitted_count: result.completedStudents, // 修正：保存已完成学生数，而不是响应数
          incomplete_count: result.incompleteCount,
          completion_rate: result.completionRate,
          school_average_score: result.schoolAverageScore,
          teacher_average_score: result.teacherAverageScore,
          total_teachers: result.totalTeachers,
          grade_statistics: JSON.stringify(result.gradeStatistics),
          class_statistics: JSON.stringify(result.classStatistics),
          teacher_ranking: JSON.stringify(result.teacherRanking),
        },
        { transaction }
      );

      // 2. 清除旧的未填写学生缓存
      await this.incompleteStudentsCacheRepository.destroy({
        where: { statistics_id: statistics.id },
        transaction,
      });

      // 3. 保存新的未填写学生缓存
      if (result.incompleteStudents && result.incompleteStudents.length > 0) {
        const cacheData = result.incompleteStudents.map(student => ({
          statistics_id: statistics.id,
          sso_student_code: student.sso_student_code,
          sso_student_name: student.sso_student_name,
          grade_code: student.grade_code,
          grade_name: student.grade_name,
          class_code: student.class_code,
          class_name: student.class_name,
          student_mobile: student.student_mobile || null,
          parent_contacts: student.parent_contacts
            ? JSON.stringify(student.parent_contacts)
            : null,
          student_status: student.student_status || null,
        }));

        await this.incompleteStudentsCacheRepository.bulkCreate(cacheData, {
          transaction,
        });
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 获取统计状态
   * @param questionnaireId 问卷ID
   * @returns 统计状态信息
   */
  async getStatisticsStatus(
    questionnaireId: number
  ): Promise<QuestionnaireStatistics | null> {
    const questionnaire = await this.questionnaireRepository.findByPk(
      questionnaireId
    );
    if (!questionnaire) {
      return null;
    }

    return await this.statisticsRepository.findOne({
      where: {
        questionnaire_id: questionnaireId,
        sso_school_code: questionnaire.sso_school_code,
        month: questionnaire.month,
      },
      order: [['updated_at', 'DESC']],
    });
  }

  /**
   * 获取缓存的统计数据
   * @param questionnaireId 问卷ID
   * @returns 缓存的统计数据
   */
  async getCachedStatistics(questionnaireId: number): Promise<any> {
    const statistics = await this.getStatisticsStatus(questionnaireId);

    if (!statistics || statistics.status !== 'completed') {
      return null;
    }

    return {
      statistics_id: statistics.id,
      questionnaire_id: statistics.questionnaire_id,
      total_students: statistics.total_students,
      submitted_count: statistics.submitted_count,
      incomplete_count: statistics.incomplete_count,
      completion_rate: statistics.completion_rate,
      school_average_score: statistics.school_average_score,
      teacher_average_score: statistics.teacher_average_score,
      total_teachers: statistics.total_teachers,
      grade_statistics: statistics.grade_statistics
        ? JSON.parse(statistics.grade_statistics)
        : {},
      class_statistics: statistics.class_statistics
        ? JSON.parse(statistics.class_statistics)
        : {},
      teacher_ranking: statistics.teacher_ranking
        ? JSON.parse(statistics.teacher_ranking)
        : [],
      last_calculated_at: statistics.last_calculated_at,
      calculation_duration: statistics.calculation_duration,
    };
  }

  /**
   * 计算年级和班级统计数据
   * @param allStudents 所有学生
   * @param submittedResponses 已提交的响应
   * @param statistics 统计记录
   * @returns 年级班级统计和未填写学生列表
   */
  private async calculateGradeClassStatistics(
    allStudents: any[],
    submittedResponses: any[],
    statistics: QuestionnaireStatistics
  ): Promise<{
    gradeStatistics: any;
    classStatistics: any;
    incompleteStudents: any[];
  }> {
    // 创建已提交学生的Set，用于快速查找
    const submittedStudentCodes = new Set(
      submittedResponses.map(r => r.sso_student_code)
    );

    const gradeStats: any = {};
    const classStats: any = {};
    const incompleteStudents: any[] = [];

    // 遍历所有学生，计算统计数据
    for (const student of allStudents) {
      // 提取年级班级编码（使用与ResponseService相同的逻辑）
      const extractedCodes = this.extractGradeAndClassCodes(student);
      const gradeCode =
        extractedCodes.gradeCode || student.classes[0]?.grade_code || '';
      const classCode =
        extractedCodes.classCode || student.classes[0]?.code || '';
      const gradeName = student.classes[0]?.grade_name || '';
      const className = student.classes[0]?.name || '';

      // 初始化年级统计
      if (!gradeStats[gradeCode]) {
        gradeStats[gradeCode] = {
          grade_code: gradeCode,
          grade_name: gradeName,
          total_students: 0,
          submitted_count: 0,
          completion_rate: 0,
        };
      }

      // 初始化班级统计
      const classKey = `${gradeCode}-${classCode}`;
      if (!classStats[classKey]) {
        classStats[classKey] = {
          grade_code: gradeCode,
          grade_name: gradeName,
          class_code: classCode,
          class_name: className,
          total_students: 0,
          submitted_count: 0,
          completion_rate: 0,
        };
      }

      // 更新统计数据
      gradeStats[gradeCode].total_students++;
      classStats[classKey].total_students++;

      const isSubmitted = submittedStudentCodes.has(student.code);
      if (isSubmitted) {
        gradeStats[gradeCode].submitted_count++;
        classStats[classKey].submitted_count++;
      } else {
        // 添加到未填写学生列表
        incompleteStudents.push({
          sso_student_code: student.code,
          sso_student_name: student.name,
          grade_code: gradeCode,
          grade_name: gradeName,
          class_code: classCode,
          class_name: className,
          student_mobile: student.mobile || null,
          parent_contacts: student.parent_contacts || null,
          student_status: student.status || '在读',
        });
      }
    }

    // 计算完成率
    Object.values(gradeStats).forEach((grade: any) => {
      grade.completion_rate =
        grade.total_students > 0
          ? Math.round((grade.submitted_count / grade.total_students) * 10000) /
            100
          : 0;
    });

    Object.values(classStats).forEach((cls: any) => {
      cls.completion_rate =
        cls.total_students > 0
          ? Math.round((cls.submitted_count / cls.total_students) * 10000) / 100
          : 0;
    });

    console.log('年级班级统计计算完成', {
      grade_count: Object.keys(gradeStats).length,
      class_count: Object.keys(classStats).length,
      incomplete_students_count: incompleteStudents.length,
    });

    return {
      gradeStatistics: gradeStats,
      classStatistics: classStats,
      incompleteStudents,
    };
  }

  /**
   * 计算教师排名
   * @param submittedResponses 已提交的响应
   * @returns 教师排名列表
   */
  private calculateTeacherRanking(submittedResponses: any[]): any[] {
    const teacherStats: { [key: string]: any } = {};

    // 统计每个教师的评分
    submittedResponses.forEach(response => {
      if (response.answers) {
        response.answers.forEach((answer: any) => {
          const teacherId = answer.sso_teacher_id;

          if (!teacherStats[teacherId]) {
            teacherStats[teacherId] = {
              teacher_id: teacherId,
              teacher_name: answer.sso_teacher_name,
              teacher_subject: answer.sso_teacher_subject,
              teacher_position: answer.sso_teacher_position,
              teacher_department: answer.sso_teacher_department,
              total_score: 0,
              evaluation_count: 0,
              average_score: 0,
            };
          }

          teacherStats[teacherId].total_score += answer.rating;
          teacherStats[teacherId].evaluation_count++;
        });
      }
    });

    // 计算平均分并排序
    const teacherRanking = Object.values(teacherStats)
      .map((teacher: any) => {
        teacher.average_score =
          teacher.evaluation_count > 0
            ? Math.round(
                (teacher.total_score / teacher.evaluation_count) * 100
              ) / 100
            : 0;
        return teacher;
      })
      .sort((a: any, b: any) => b.average_score - a.average_score);

    console.log('教师排名计算完成', {
      teacher_count: teacherRanking.length,
      top_score: teacherRanking[0]?.average_score || 0,
    });

    return teacherRanking;
  }

  /**
   * 从学生信息中提取年级编号和班级编号
   * 与ResponseService中的逻辑保持一致
   */
  private extractGradeAndClassCodes(studentInfo: any): {
    gradeCode: string | null;
    classCode: string | null;
  } {
    let gradeCode: string | null = null;
    let classCode: string | null = null;

    // 获取班级和年级信息
    const classInfo = studentInfo.classes?.[0];
    if (!classInfo) {
      return { gradeCode, classCode };
    }

    const gradeName = classInfo.grade_name || '';
    const className = classInfo.name || '';

    // 从年级信息中提取年级编号
    if (gradeName) {
      gradeCode = this.extractNumberFromText(gradeName, '年级');
    }

    // 从班级信息中提取班级编号
    if (className) {
      classCode = this.extractNumberFromText(className, '班级');
    }

    return { gradeCode, classCode };
  }

  /**
   * 从文本中提取数字编号（与ResponseService保持一致）
   */
  private extractNumberFromText(text: string, type: string): string | null {
    if (!text || typeof text !== 'string') {
      return null;
    }

    // 1. 尝试提取阿拉伯数字（优先级最高）
    const arabicMatch = text.match(/(\d+)/);
    if (arabicMatch) {
      return arabicMatch[1];
    }

    // 2. 特殊情况处理
    const specialCases: { [key: string]: string } = {
      初一: '7',
      初二: '8',
      初三: '9',
      初中一年级: '7',
      初中二年级: '8',
      初中三年级: '9',
      高一: '10',
      高二: '11',
      高三: '12',
      高中一年级: '10',
      高中二年级: '11',
      高中三年级: '12',
      小一: '1',
      小二: '2',
      小三: '3',
      小四: '4',
      小五: '5',
      小六: '6',
      小学一年级: '1',
      小学二年级: '2',
      小学三年级: '3',
      小学四年级: '4',
      小学五年级: '5',
      小学六年级: '6',
    };

    for (const [special, number] of Object.entries(specialCases)) {
      if (text.includes(special)) {
        return number;
      }
    }

    // 3. 中文数字转换
    const chineseNumbers: { [key: string]: string } = {
      十二: '12',
      十一: '11',
      十: '10',
      九: '9',
      八: '8',
      七: '7',
      六: '6',
      五: '5',
      四: '4',
      三: '3',
      二: '2',
      一: '1',
    };

    for (const [chinese, number] of Object.entries(chineseNumbers)) {
      if (text.includes(chinese)) {
        return number;
      }
    }

    // 4. 英文字母转换
    const englishMatch = text.match(/([A-Za-z]+)/);
    if (englishMatch) {
      const letter = englishMatch[1].toUpperCase();
      if (letter.length === 1 && letter >= 'A' && letter <= 'Z') {
        return (letter.charCodeAt(0) - 'A'.charCodeAt(0) + 1).toString();
      }
    }

    return null;
  }
}
