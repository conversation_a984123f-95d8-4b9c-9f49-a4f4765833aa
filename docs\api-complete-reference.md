# 教师评价问卷系统 - 完整接口文档

## 📋 基础信息

- **Base URL**: `http://localhost:3141`
- **Content-Type**: `application/json`
- **认证方式**: JWT <PERSON>ken (Header: `Authorization: Bearer <token>`)

## 🔄 统一响应格式

所有接口都经过 `FormatMiddleware` 和 `DefaultErrorFilter` 处理，返回统一格式：

```json
{
  "errCode": 0,           // 错误码，0表示成功
  "msg": "操作成功",       // 响应消息
  "data": {},             // 响应数据
  "timestamp": 1705312200000  // 时间戳
}
```

**错误码说明：**
- `0`: 成功
- `400`: 业务错误
- `401`: 认证失败
- `404`: 资源不存在
- `422`: 参数错误
- `500`: 系统错误

---

## 🗂️ 问卷管理场景

### 1. 创建问卷
- **URL**: `POST /api/questionnaire`
- **认证**: 需要JWT
- **入参**:
```json
{
  "title": "2024年1月教师评价问卷",
  "description": "请对本月教师表现进行评价",
  "month": "2024-01",
  "sso_school_code": "school_001",
  "star_rating_mode": 5,
  "include_school_evaluation": true,
  "instructions": "请客观公正地评价",
  "allow_anonymous": false,
  "max_teachers_limit": 10,
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-31T23:59:59Z"
}
```
- **返回**:
```json
{
  "errCode": 0,
  "msg": "问卷创建成功",
  "data": {
    "id": 1,
    "title": "2024年1月教师评价问卷",
    "status": "draft",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 获取问卷列表
- **URL**: `GET /api/questionnaire`
- **认证**: 需要JWT
- **入参**: Query参数
  - `sso_school_code`: 学校编码 (可选)
  - `month`: 月份 YYYY-MM (可选)
  - `status`: 状态 draft/published/closed (可选)
  - `page`: 页码，默认1
  - `limit`: 每页数量，默认10
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取问卷列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "2024年1月教师评价问卷",
        "month": "2024-01",
        "status": "published",
        "star_rating_mode": 5
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  }
}
```

### 3. 获取问卷详情
- **URL**: `GET /api/questionnaire/:id`
- **认证**: 需要JWT
- **入参**: 路径参数 `id` (问卷ID)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取问卷详情成功",
  "data": {
    "id": 1,
    "title": "2024年1月教师评价问卷",
    "description": "请对本月教师表现进行评价",
    "month": "2024-01",
    "sso_school_code": "school_001",
    "star_rating_mode": 5,
    "status": "published",
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-01-31T23:59:59Z"
  }
}
```

### 4. 更新问卷状态
- **URL**: `PUT /api/questionnaire/:id/status`
- **认证**: 需要JWT
- **入参**:
```json
{
  "status": "published"
}
```
- **返回**:
```json
{
  "errCode": 0,
  "msg": "问卷状态更新成功",
  "data": {
    "id": 1,
    "status": "published",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5. 更新问卷
- **URL**: `PUT /api/questionnaire/:id`
- **认证**: 需要JWT
- **入参**: 同创建问卷，字段可选
- **返回**: 同获取问卷详情

### 6. 删除问卷
- **URL**: `DELETE /api/questionnaire/:id`
- **认证**: 需要JWT
- **入参**: 路径参数 `id` (问卷ID)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "问卷删除成功",
  "data": null
}
```

---

## 👨‍👩‍👧‍👦 家长验证场景

### 1. 验证家长手机号
- **URL**: `POST /api/parent/verify-phone`
- **认证**: 不需要
- **入参**:
```json
{
  "phone": "13800138000"
}
```
- **返回**:
```json
{
  "errCode": 0,
  "msg": "手机号验证成功",
  "data": {
    "is_valid": true,
    "message": "验证成功",
    "parent": {
      "id": "parent_001",
      "name": "张三家长",
      "mobile": "13800138000",
      "children": [
        {
          "id": "student_001",
          "student": {
            "id": "student_001",
            "name": "张小明",
            "class": "三年级1班"
          }
        }
      ]
    }
  }
}
```

### 2. 查询学生可填写问卷
- **URL**: `GET /api/parent/student-questionnaires`
- **认证**: 不需要
- **入参**: Query参数
  - `sso_school_code`: 学校编码 (必填)
  - `sso_student_code`: 学生编码 (必填)
  - `parent_phone`: 家长手机号 (必填)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "查询完成",
  "data": {
    "has_questionnaire": true,
    "questionnaire": {
      "id": 1,
      "title": "2024年1月教师评价问卷",
      "month": "2024-01",
      "is_submitted": false,
      "star_rating_mode": 5
    }
  }
}
```

---

## 📝 问卷填写场景

### 1. 提交问卷响应
- **URL**: `POST /api/response`
- **认证**: 不需要
- **入参**:
```json
{
  "questionnaire_id": 1,
  "parent_phone": "13800138000",
  "parent_name": "张三家长",
  "sso_student_code": "student_001",
  "sso_student_name": "张小明",
  "grade_code": "3",
  "class_code": "1",
  "month": "2024-01",
  "school_rating": 85,
  "school_description": "学校整体表现很好",
  "teacher_evaluations": [
    {
      "sso_teacher_id": "teacher_001",
      "rating": 90,
      "description": "老师教学认真负责"
    }
  ],
  "remarks": "整体评价很好"
}
```
- **返回**:
```json
{
  "errCode": 0,
  "msg": "问卷提交成功",
  "data": {
    "response_id": 123,
    "questionnaire_id": 1,
    "submission_time": "2024-01-15T10:30:00Z",
    "submitted_at": "2024-01-15T10:30:00Z",
    "total_average_score": 88.5,
    "teacher_count": 1,
    "ip_address": "*************",
    "phone_verified": false
  }
}
```

### 2. 获取问卷填写信息（回填）
- **URL**: `GET /api/response/edit`
- **认证**: 不需要
- **入参**: Query参数
  - `questionnaire_id`: 问卷ID (必填)
  - `parent_phone`: 家长手机号 (必填)
  - `sso_student_code`: 学生编码 (必填)
  - `month`: 月份 YYYY-MM (必填)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取问卷填写信息成功",
  "data": {
    "response_id": 123,
    "questionnaire_id": 1,
    "parent_phone": "13800138000",
    "sso_student_code": "student_001",
    "month": "2024-01",
    "school_rating": 85,
    "school_description": "学校整体表现很好",
    "teacher_evaluations": [
      {
        "sso_teacher_id": "teacher_001",
        "sso_teacher_name": "李老师",
        "rating": 90,
        "description": "老师教学认真负责"
      }
    ]
  }
}

### 4. 获取响应列表
- **URL**: `GET /api/response`
- **认证**: 不需要
- **入参**: Query参数
  - `questionnaire_id`: 问卷ID (可选)
  - `parent_phone`: 家长手机号 (可选)
  - `sso_student_code`: 学生编码 (可选)
  - `month`: 月份 (可选)
  - `grade_code`: 年级编号 (可选)
  - `class_code`: 班级编号 (可选)
  - `is_completed`: 是否完成 (可选)
  - `page`: 页码，默认1
  - `limit`: 每页数量，默认10
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取响应列表成功",
  "data": {
    "list": [
      {
        "id": 123,
        "questionnaire_id": 1,
        "parent_phone": "13800138000",
        "sso_student_name": "张小明",
        "total_average_score": 88.5,
        "teacher_count": 2,
        "is_completed": true,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  }
}
```

### 5. 获取响应详情
- **URL**: `GET /api/response/:id`
- **认证**: 不需要
- **入参**: 路径参数 `id` (响应ID)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取响应详情成功",
  "data": {
    "id": 123,
    "questionnaire_id": 1,
    "parent_phone": "13800138000",
    "parent_name": "张三家长",
    "sso_student_name": "张小明",
    "school_rating": 85,
    "school_description": "学校整体表现很好",
    "answers": [
      {
        "sso_teacher_id": "teacher_001",
        "sso_teacher_name": "李老师",
        "rating": 90,
        "description": "老师教学认真负责"
      }
    ]
  }
}
```

### 6. 获取问卷统计信息
- **URL**: `GET /api/response/statistics/:questionnaireId`
- **认证**: 不需要
- **入参**: 路径参数 `questionnaireId` (问卷ID)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取统计信息成功",
  "data": {
    "questionnaire_id": 1,
    "total_responses": 50,
    "completed_responses": 48,
    "completion_rate": 96.0,
    "average_rating": 87.5,
    "teacher_evaluation_count": 120
  }
}
```

### 7. 获取评分系统说明
- **URL**: `GET /api/response/rating-info`
- **认证**: 不需要
- **入参**: 无
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取评分系统说明成功",
  "data": {
    "school_rating": {
      "system": "100分制",
      "description": "学校评分采用100分制，范围为0-100分",
      "grade_levels": [
        { "grade": "A+", "range": "90-100分", "description": "优秀" },
        { "grade": "A", "range": "80-89分", "description": "良好" }
      ]
    },
    "teacher_rating": {
      "system": "100分制",
      "description": "教师评分采用100分制，范围为0-100分"
    }
  }
}
```

---

## 📊 统计分析场景

### 1. 获取学校统计概览
- **URL**: `GET /api/statistics/school`
- **认证**: 需要JWT
- **入参**: Query参数
  - `sso_school_code`: 学校编码 (必填)
  - `month`: 月份 YYYY-MM (可选)
  - `start_month`: 开始月份 (可选)
  - `end_month`: 结束月份 (可选)
  - `include_trend`: 是否包含趋势数据 (可选)
  - `include_ranking`: 是否包含排名数据 (可选)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "操作成功",
  "data": {
    "school_code": "school_001",
    "month": "2024-01",
    "total_responses": 150,
    "average_score": 87.5,
    "teacher_count": 25,
    "response_rate": 85.2,
    "top_teachers": [
      {
        "sso_teacher_id": "teacher_001",
        "sso_teacher_name": "李老师",
        "average_score": 95.5,
        "evaluation_count": 12
      }
    ]
  }
}
```

### 2. 获取教师统计概览
- **URL**: `GET /api/statistics/teacher`
- **认证**: 需要JWT
- **入参**: Query参数
  - `sso_teacher_id`: 教师ID (必填)
  - `sso_school_code`: 学校编码 (可选)
  - `month`: 月份 (可选)
  - `start_month`: 开始月份 (可选)
  - `end_month`: 结束月份 (可选)
  - `include_distribution`: 是否包含评分分布 (可选)
  - `include_keywords`: 是否包含关键词云 (可选)
  - `include_trend`: 是否包含趋势数据 (可选)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "操作成功",
  "data": {
    "teacher_id": "teacher_001",
    "teacher_name": "李老师",
    "month": "2024-01",
    "average_score": 92.5,
    "evaluation_count": 15,
    "recommendation_rate": 93.3,
    "score_distribution": {
      "90-100": 12,
      "80-89": 3,
      "70-79": 0
    },
    "keywords": [
      { "word": "认真", "count": 8 },
      { "word": "负责", "count": 6 }
    ]
  }
}

### 3. 获取教师排名
- **URL**: `GET /api/statistics/teacher-ranking`
- **认证**: 需要JWT
- **入参**: Query参数
  - `sso_school_code`: 学校编码 (必填)
  - `month`: 月份 (可选)
  - `subject`: 科目筛选 (可选)
  - `department`: 部门筛选 (可选)
  - `page`: 页码，默认1
  - `limit`: 每页数量，默认20
  - `sort_by`: 排序字段 average_score/evaluation_count/recommendation_rate (可选)
  - `sort_order`: 排序方向 ASC/DESC (可选)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "操作成功",
  "data": {
    "list": [
      {
        "rank": 1,
        "sso_teacher_id": "teacher_001",
        "sso_teacher_name": "李老师",
        "sso_teacher_subject": "数学",
        "average_score": 95.5,
        "evaluation_count": 12,
        "recommendation_rate": 100.0
      }
    ],
    "total": 25,
    "page": 1,
    "limit": 20
  }
}
```

### 4. 获取趋势分析
- **URL**: `GET /api/statistics/trend`
- **认证**: 需要JWT
- **入参**: Query参数
  - `sso_school_code`: 学校编码 (可选)
  - `sso_teacher_id`: 教师ID (可选)
  - `start_month`: 开始月份 YYYY-MM (必填)
  - `end_month`: 结束月份 YYYY-MM (必填)
  - `analysis_type`: 分析类型 school/teacher (可选)
  - `granularity`: 粒度 month/week (可选)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "操作成功",
  "data": {
    "analysis_type": "school",
    "start_month": "2024-01",
    "end_month": "2024-03",
    "trend_data": [
      {
        "month": "2024-01",
        "average_score": 87.5,
        "response_count": 150,
        "teacher_count": 25
      },
      {
        "month": "2024-02",
        "average_score": 89.2,
        "response_count": 165,
        "teacher_count": 26
      }
    ]
  }
}
```

### 5. 获取教师评分分布
- **URL**: `GET /api/statistics/teacher/:teacherId/distribution`
- **认证**: 需要JWT
- **入参**:
  - 路径参数: `teacherId` (教师ID)
  - Query参数: `sso_school_code` (学校编码), `month` (月份)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "操作成功",
  "data": {
    "teacher_id": "teacher_001",
    "month": "2024-01",
    "distribution": {
      "90-100": 12,
      "80-89": 3,
      "70-79": 0,
      "60-69": 0,
      "0-59": 0
    },
    "total_evaluations": 15
  }
}
```

### 6. 获取教师关键词云
- **URL**: `GET /api/statistics/teacher/:teacherId/keywords`
- **认证**: 需要JWT
- **入参**:
  - 路径参数: `teacherId` (教师ID)
  - Query参数: `sso_school_code` (学校编码), `month` (月份)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "操作成功",
  "data": {
    "teacher_id": "teacher_001",
    "month": "2024-01",
    "keywords": [
      { "word": "认真", "count": 8, "weight": 0.8 },
      { "word": "负责", "count": 6, "weight": 0.6 },
      { "word": "专业", "count": 5, "weight": 0.5 }
    ],
    "total_descriptions": 15
  }
}
```

### 7. 获取学校响应趋势
- **URL**: `GET /api/statistics/school/:schoolId/trend`
- **认证**: 需要JWT
- **入参**:
  - 路径参数: `schoolId` (学校ID)
  - Query参数: `start_month`, `end_month` (时间范围)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "操作成功",
  "data": {
    "school_id": "school_001",
    "trend_data": [
      {
        "month": "2024-01",
        "response_count": 150,
        "average_score": 87.5,
        "completion_rate": 85.2
      }
    ]
  }
}
```

### 8. 获取教师评价趋势
- **URL**: `GET /api/statistics/teacher/:teacherId/trend`
- **认证**: 需要JWT
- **入参**:
  - 路径参数: `teacherId` (教师ID)
  - Query参数: `sso_school_code`, `start_month`, `end_month`
- **返回**:
```json
{
  "errCode": 0,
  "msg": "操作成功",
  "data": {
    "teacher_id": "teacher_001",
    "trend_data": [
      {
        "month": "2024-01",
        "average_score": 92.5,
        "evaluation_count": 15,
        "recommendation_rate": 93.3
      }
    ]
  }
}
```

---

## 🔍 SSO集成场景

### 1. 获取班级教师列表
- **URL**: `GET /api/sso/teachers/:enterpriseCode`
- **认证**: 不需要
- **入参**:
  - 路径参数: `enterpriseCode` (学校编码)
  - Query参数: `gradeCode` (年级编码), `classCode` (班级编码)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取班级教师列表成功",
  "data": [
    {
      "id": "teacher_001",
      "name": "李老师",
      "subject": "数学",
      "position": "班主任",
      "department": "数学组"
    }
  ]
}
```

### 2. 获取学校成员列表
- **URL**: `GET /api/sso/members/:enterpriseCode`
- **认证**: 不需要
- **入参**: 路径参数 `enterpriseCode` (学校编码)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取成员列表成功",
  "data": {
    "count": 100,
    "list": [
      {
        "id": "member_001",
        "name": "张老师",
        "type": "teacher",
        "department": "语文组"
      }
    ]
  }
}

### 3. 获取成员详细信息
- **URL**: `GET /api/sso/member/:enterpriseCode/:memberCode`
- **认证**: 不需要
- **入参**: 路径参数 `enterpriseCode` (学校编码), `memberCode` (成员编码)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取成员信息成功",
  "data": {
    "id": "member_001",
    "name": "张老师",
    "code": "teacher_001",
    "employment_status": "在岗",
    "department": "语文组"
  }
}
```

### 4. 获取学校信息
- **URL**: `GET /api/sso/enterprise/:enterpriseCode`
- **认证**: 不需要
- **入参**: 路径参数 `enterpriseCode` (学校编码)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取学校信息成功",
  "data": {
    "id": "school_001",
    "name": "示例小学",
    "code": "school_001",
    "address": "北京市朝阳区"
  }
}
```

### 5. 获取学期列表
- **URL**: `GET /api/sso/semesters/:enterpriseCode`
- **认证**: 不需要
- **入参**: 路径参数 `enterpriseCode` (学校编码)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取学期列表成功",
  "data": [
    {
      "id": "semester_001",
      "name": "2024年春季学期",
      "start_date": "2024-02-01",
      "end_date": "2024-07-31"
    }
  ]
}
```

### 6. 测试年级班级组合
- **URL**: `GET /api/sso/test-grade-class/:enterpriseCode`
- **认证**: 不需要
- **入参**: 路径参数 `enterpriseCode` (学校编码)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "测试完成",
  "data": {
    "enterprise_code": "school_001",
    "test_results": [
      {
        "gradeCode": "1",
        "classCode": "1",
        "success": true,
        "teacherCount": 5,
        "data": []
      }
    ]
  }
}
```

---

## 📋 操作日志场景

### 1. 创建操作日志
- **URL**: `POST /api/operation-log`
- **认证**: 需要JWT
- **入参**:
```json
{
  "module": "QUESTIONNAIRE",
  "operation_type": "CREATE",
  "operation_description": "创建问卷",
  "target_id": "1",
  "target_type": "questionnaire",
  "operation_details": {
    "title": "2024年1月教师评价问卷"
  }
}
```
- **返回**:
```json
{
  "errCode": 0,
  "msg": "操作日志创建成功",
  "data": {
    "id": 1,
    "operator_user_id": "user_001",
    "operation_description": "创建问卷",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 获取操作日志列表
- **URL**: `GET /api/operation-log`
- **认证**: 需要JWT
- **入参**: Query参数
  - `operator_user_id`: 操作用户ID (可选)
  - `operator_school_code`: 操作学校编码 (可选)
  - `module`: 操作模块 (可选)
  - `operation_type`: 操作类型 (可选)
  - `start_time`: 开始时间 (可选)
  - `end_time`: 结束时间 (可选)
  - `page`: 页码，默认1
  - `limit`: 每页数量，默认10
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取操作日志列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "operator_user_id": "user_001",
        "operator_user_name": "管理员",
        "module": "QUESTIONNAIRE",
        "operation_type": "CREATE",
        "operation_description": "创建问卷",
        "target_id": "1",
        "operation_status": "SUCCESS",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  }
}
```

### 3. 获取操作日志统计
- **URL**: `GET /api/operation-log/statistics`
- **认证**: 需要JWT
- **入参**: Query参数
  - `operator_school_code`: 学校编码 (可选)
  - `module`: 操作模块 (可选)
  - `start_time`: 开始时间 (可选)
  - `end_time`: 结束时间 (可选)
  - `granularity`: 统计粒度 day/week/month (可选)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取操作日志统计成功",
  "data": {
    "total_operations": 150,
    "success_operations": 145,
    "failed_operations": 5,
    "success_rate": 96.7,
    "daily_statistics": [
      {
        "date": "2024-01-01",
        "total": 25,
        "success": 24,
        "failed": 1
      }
    ]
  }
}

### 4. 获取用户操作历史
- **URL**: `GET /api/operation-log/user/:userId/history`
- **认证**: 需要JWT
- **入参**:
  - 路径参数: `userId` (用户ID)
  - Query参数: `page`, `limit`, `start_time`, `end_time`
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取用户操作历史成功",
  "data": {
    "user_id": "user_001",
    "list": [
      {
        "operation_description": "创建问卷",
        "module": "QUESTIONNAIRE",
        "operation_status": "SUCCESS",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1
  }
}
```

### 5. 获取学校操作日志
- **URL**: `GET /api/operation-log/school/:schoolId`
- **认证**: 需要JWT
- **入参**:
  - 路径参数: `schoolId` (学校ID)
  - Query参数: `page`, `limit`, `start_time`, `end_time`
- **返回**: 同操作日志列表格式

### 6. 获取操作日志趋势
- **URL**: `GET /api/operation-log/trend`
- **认证**: 需要JWT
- **入参**: Query参数
  - `start_time`: 开始时间 (必填)
  - `end_time`: 结束时间 (必填)
  - `granularity`: 粒度 day/week/month (可选)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "获取操作日志趋势成功",
  "data": {
    "trend_data": [
      {
        "date": "2024-01-01",
        "total_operations": 25,
        "success_operations": 24,
        "failed_operations": 1
      }
    ]
  }
}
```

### 7. 清理过期日志
- **URL**: `POST /api/operation-log/cleanup`
- **认证**: 需要JWT
- **入参**:
```json
{
  "days_to_keep": 90
}
```
- **返回**:
```json
{
  "errCode": 0,
  "msg": "清理过期日志成功",
  "data": {
    "deleted_count": 150,
    "cleanup_date": "2024-01-01T00:00:00Z"
  }
}
```

---

## 🔒 认证说明

### JWT Token 获取
系统使用JWT认证，需要通过SSO系统获取token，然后在请求头中携带：
```
Authorization: Bearer <your-jwt-token>
```

### 白名单路径
以下路径不需要JWT认证：
- `/api/parent/*` - 家长相关接口
- `/api/response/*` - 问卷填写相关接口
- `/api/sso/*` - SSO集成接口
- `/auth/*` - 认证相关接口

### 错误处理
所有认证失败的请求都会返回401状态码：
```json
{
  "errCode": 401,
  "msg": "认证失败，请重新登录",
  "data": null
}
```

---

## 📝 注意事项

1. **时间格式**: 所有时间字段使用ISO 8601格式 (YYYY-MM-DDTHH:mm:ssZ)
2. **月份格式**: 月份参数使用YYYY-MM格式
3. **分页**: 默认页码从1开始，默认每页10条记录
4. **评分制**: 系统内部统一使用100分制，前端可根据问卷设置转换为5星或10星显示
5. **错误处理**: 所有接口都经过统一的错误处理，返回标准格式
6. **日志记录**: 重要操作会自动记录到操作日志中
7. **中间件处理**:
   - `FormatMiddleware`: 统一包装响应格式
   - `OperationLogMiddleware`: 自动记录操作日志
   - `JwtAuthMiddleware`: JWT认证验证
   - `DefaultErrorFilter`: 统一错误处理

## 📊 接口总览表

| 场景 | 接口数量 | 认证要求 | 主要功能 |
|------|----------|----------|----------|
| 问卷管理 | 6个 | 需要JWT | 问卷CRUD操作 |
| 家长验证 | 2个 | 不需要 | 手机号验证、问卷查询 |
| 问卷填写 | 7个 | 不需要 | 提交响应、查询填写信息 |
| 统计分析 | 8个 | 需要JWT | 学校/教师统计、趋势分析 |
| SSO集成 | 6个 | 不需要 | 获取学校、教师、学生信息 |
| 操作日志 | 7个 | 需要JWT | 日志记录、查询、统计 |

**总计**: 36个接口，其中21个需要JWT认证，15个无需认证。
```
```
```
```

### 3. 检查重复提交
- **URL**: `GET /api/response/check`
- **认证**: 不需要
- **入参**: Query参数
  - `parent_phone`: 家长手机号 (必填)
  - `questionnaire_id`: 问卷ID (必填)
  - `sso_student_code`: 学生编码 (必填)
  - `month`: 月份 YYYY-MM (必填)
- **返回**:
```json
{
  "errCode": 0,
  "msg": "检查完成",
  "data": {
    "exists": false,
    "message": "可以提交问卷"
  }
}
```
