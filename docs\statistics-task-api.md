# 统计任务API文档

## 概述

为了解决实时统计查询的性能问题，我们引入了统计任务机制。用户可以手动触发统计计算，系统将统计结果缓存到数据库中，后续查询直接从缓存读取，大大提升查询性能。

## API接口

### 1. 触发统计计算

**接口**: `POST /api/statistics-task/trigger`

**描述**: 手动触发问卷统计计算，系统将异步执行统计任务

**请求参数**:
```json
{
  "questionnaire_id": 1
}
```

**返回数据**:
```json
{
  "errCode": 0,
  "msg": "统计任务已启动",
  "data": {
    "statistics_id": 123,
    "questionnaire_id": 1,
    "status": "pending",
    "triggered_by": "admin",
    "created_at": "2024-12-26T10:30:00Z"
  }
}
```

**状态说明**:
- `pending`: 等待计算
- `calculating`: 计算中
- `completed`: 计算完成
- `failed`: 计算失败

### 2. 获取统计状态

**接口**: `GET /api/statistics-task/status/{questionnaireId}`

**描述**: 查询统计任务的执行状态和基本信息

**路径参数**:
- `questionnaireId`: 问卷ID

**返回数据**:
```json
{
  "errCode": 0,
  "msg": "获取统计状态成功",
  "data": {
    "statistics_id": 123,
    "questionnaire_id": 1,
    "status": "completed",
    "last_calculated_at": "2024-12-26T10:35:00Z",
    "calculation_duration": 5000,
    "error_message": null,
    "triggered_by": "admin",
    "total_students": 500,
    "submitted_count": 450,
    "completion_rate": 90.00
  }
}
```

### 3. 获取缓存的统计数据

**接口**: `GET /api/statistics-task/cached/{questionnaireId}`

**描述**: 获取已缓存的完整统计数据

**路径参数**:
- `questionnaireId`: 问卷ID

**返回数据**:
```json
{
  "errCode": 0,
  "msg": "获取缓存统计数据成功",
  "data": {
    "statistics_id": 123,
    "questionnaire_id": 1,
    "total_students": 500,
    "submitted_count": 450,
    "incomplete_count": 50,
    "completion_rate": 90.00,
    "school_average_score": 85.5,
    "teacher_average_score": 87.2,
    "total_teachers": 25,
    "grade_statistics": {
      "1": {
        "grade_name": "一年级",
        "total_students": 100,
        "submitted_count": 95,
        "completion_rate": 95.00
      }
    },
    "class_statistics": {
      "1-1": {
        "grade_name": "一年级",
        "class_name": "1班",
        "total_students": 30,
        "submitted_count": 28,
        "completion_rate": 93.33
      }
    },
    "teacher_ranking": [
      {
        "teacher_id": "T001",
        "teacher_name": "张老师",
        "average_score": 95.5,
        "evaluation_count": 120
      }
    ],
    "last_calculated_at": "2024-12-26T10:35:00Z",
    "calculation_duration": 5000
  }
}
```

### 4. 获取缓存的未填写学生列表

**接口**: `GET /api/statistics-task/incomplete-students`

**描述**: 从缓存中获取未填写问卷的学生列表（支持分页和筛选）

**请求参数**:
```json
{
  "questionnaire_id": 1,
  "grade_code": "4",
  "class_code": "5",
  "page": 1,
  "pageSize": 20
}
```

**返回数据**:
```json
{
  "errCode": 0,
  "msg": "获取未填写学生列表成功",
  "data": {
    "status": "completed",
    "last_calculated_at": "2024-12-26T10:35:00Z",
    "list": [
      {
        "sso_student_code": "S001",
        "sso_student_name": "李小明",
        "grade_code": "4",
        "grade_name": "四年级",
        "class_code": "5",
        "class_name": "5班",
        "student_mobile": "13800138001",
        "parent_contacts": [
          {
            "name": "李父",
            "phone": "13800138002",
            "relation": "父亲"
          }
        ],
        "student_status": "在读"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "totalPages": 3
  }
}
```

## 使用流程

### 1. 前端使用流程

```javascript
// 1. 触发统计计算
const triggerResponse = await fetch('/api/statistics-task/trigger', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ questionnaire_id: 1 })
});

// 2. 轮询检查状态
const checkStatus = async () => {
  const statusResponse = await fetch('/api/statistics-task/status/1');
  const statusData = await statusResponse.json();
  
  if (statusData.data.status === 'completed') {
    // 统计完成，可以获取数据
    loadStatisticsData();
  } else if (statusData.data.status === 'calculating') {
    // 仍在计算中，继续轮询
    setTimeout(checkStatus, 2000);
  } else if (statusData.data.status === 'failed') {
    // 计算失败
    console.error('统计计算失败:', statusData.data.error_message);
  }
};

// 3. 获取统计数据
const loadStatisticsData = async () => {
  const dataResponse = await fetch('/api/statistics-task/cached/1');
  const statisticsData = await dataResponse.json();
  // 处理统计数据
};
```

### 2. 管理员使用流程

1. **手动触发统计**: 在管理界面点击"刷新统计"按钮
2. **查看进度**: 实时显示统计计算进度和状态
3. **查看结果**: 统计完成后查看详细的统计报告
4. **导出数据**: 基于缓存数据进行快速导出

## 性能优势

### 1. 查询性能提升

- **实时查询**: 每次查询需要3-10秒，涉及大量数据库操作
- **缓存查询**: 查询时间降至100-500毫秒，直接读取缓存

### 2. 数据库负载降低

- **实时查询**: 每次查询产生10+个复杂SQL
- **缓存查询**: 每次查询只需1-2个简单SQL

### 3. 用户体验改善

- **按需计算**: 用户主动触发，避免不必要的计算
- **进度可见**: 实时显示计算进度，用户体验更好
- **快速响应**: 查询结果秒级返回

## 注意事项

### 1. 数据一致性

- 统计数据基于触发时的快照，不会实时更新
- 如需最新数据，请重新触发统计计算
- 建议在问卷填写高峰期后手动刷新统计

### 2. 存储空间

- 统计缓存会占用额外的数据库空间
- 系统会自动清理过期的统计数据
- 建议定期清理不需要的历史统计

### 3. 计算时间

- 大型学校（1000+学生）的统计计算可能需要10-30秒
- 计算期间请勿重复触发
- 系统会防止并发计算同一问卷的统计

## 错误处理

### 常见错误码

- `1001`: 问卷不存在
- `1002`: 统计正在计算中
- `1003`: 统计计算失败
- `1004`: 缓存数据不存在

### 错误处理建议

```javascript
const handleError = (error) => {
  switch (error.errCode) {
    case 1002:
      // 正在计算中，继续等待
      setTimeout(checkStatus, 2000);
      break;
    case 1004:
      // 无缓存数据，提示用户触发统计
      showMessage('请先触发统计计算');
      break;
    default:
      showError(error.msg);
  }
};
```
