import { Controller, Post, Get, Inject, Body, Query, Param } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate, Rule, RuleType } from '@midwayjs/validate';
import { StatisticsTaskSimpleService } from '../service/statistics-task-simple.service';

/**
 * 触发统计计算DTO
 */
class TriggerStatisticsDTO {
  @Rule(RuleType.number().integer().min(1).required())
  questionnaire_id: number;
}

/**
 * 未填写学生查询DTO（基于缓存）
 */
class CachedIncompleteStudentsQueryDTO {
  @Rule(RuleType.number().integer().min(1).required())
  questionnaire_id: number;

  @Rule(RuleType.string().max(20).optional())
  grade_code?: string;

  @Rule(RuleType.string().max(20).optional())
  class_code?: string;

  @Rule(RuleType.number().integer().min(1).default(1))
  page?: number;

  @Rule(RuleType.number().integer().min(1).max(100).default(20))
  pageSize?: number;
}

/**
 * 统计任务控制器
 */
@Controller('/api/statistics-task')
export class StatisticsTaskController {
  @Inject()
  statisticsTaskService: StatisticsTaskSimpleService;

  @Inject()
  ctx: Context;

  /**
   * 触发统计计算
   */
  @Post('/trigger')
  @Validate()
  async triggerStatistics(@Body() triggerDto: TriggerStatisticsDTO) {
    try {
      // 从JWT或session中获取用户ID，这里暂时使用固定值
      const triggeredBy = this.ctx.state?.user?.id || 'system';

      this.ctx.logger.info('触发统计计算请求', {
        questionnaire_id: triggerDto.questionnaire_id,
        triggered_by: triggeredBy,
      });

      const statistics = await this.statisticsTaskService.triggerStatisticsCalculation(
        triggerDto.questionnaire_id,
        triggeredBy
      );

      return {
        errCode: 0,
        msg: '统计任务已启动',
        data: {
          statistics_id: statistics.id,
          questionnaire_id: statistics.questionnaire_id,
          status: statistics.status,
          triggered_by: statistics.triggered_by,
          created_at: statistics.created_at,
        },
      };
    } catch (error) {
      this.ctx.logger.error('触发统计计算失败', {
        questionnaire_id: triggerDto.questionnaire_id,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '触发统计计算失败',
        data: null,
      };
    }
  }

  /**
   * 获取统计状态
   */
  @Get('/status/:questionnaireId')
  async getStatisticsStatus(@Param('questionnaireId') questionnaireId: number) {
    try {
      this.ctx.logger.info('获取统计状态请求', {
        questionnaire_id: questionnaireId,
      });

      const statistics = await this.statisticsTaskService.getStatisticsStatus(questionnaireId);

      if (!statistics) {
        return {
          errCode: 0,
          msg: '暂无统计数据',
          data: {
            status: 'not_started',
            message: '尚未开始统计计算',
          },
        };
      }

      return {
        errCode: 0,
        msg: '获取统计状态成功',
        data: {
          statistics_id: statistics.id,
          questionnaire_id: statistics.questionnaire_id,
          status: statistics.status,
          last_calculated_at: statistics.last_calculated_at,
          calculation_duration: statistics.calculation_duration,
          error_message: statistics.error_message,
          triggered_by: statistics.triggered_by,
          total_students: statistics.total_students,
          submitted_count: statistics.submitted_count,
          completion_rate: statistics.completion_rate,
        },
      };
    } catch (error) {
      this.ctx.logger.error('获取统计状态失败', {
        questionnaire_id: questionnaireId,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '获取统计状态失败',
        data: null,
      };
    }
  }

  /**
   * 获取缓存的统计数据
   */
  @Get('/cached/:questionnaireId')
  async getCachedStatistics(@Param('questionnaireId') questionnaireId: number) {
    try {
      this.ctx.logger.info('获取缓存统计数据请求', {
        questionnaire_id: questionnaireId,
      });

      const cachedData = await this.statisticsTaskService.getCachedStatistics(questionnaireId);

      if (!cachedData) {
        return {
          errCode: 0,
          msg: '暂无缓存数据，请先触发统计计算',
          data: null,
        };
      }

      return {
        errCode: 0,
        msg: '获取缓存统计数据成功',
        data: cachedData,
      };
    } catch (error) {
      this.ctx.logger.error('获取缓存统计数据失败', {
        questionnaire_id: questionnaireId,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '获取缓存统计数据失败',
        data: null,
      };
    }
  }

  /**
   * 获取缓存的未填写学生列表（分页）
   */
  @Get('/incomplete-students')
  @Validate()
  async getCachedIncompleteStudents(@Query() queryDto: CachedIncompleteStudentsQueryDTO) {
    try {
      this.ctx.logger.info('获取缓存未填写学生列表请求', {
        questionnaire_id: queryDto.questionnaire_id,
        grade_code: queryDto.grade_code,
        class_code: queryDto.class_code,
        page: queryDto.page,
        pageSize: queryDto.pageSize,
      });

      const result = await this.statisticsTaskService.getCachedIncompleteStudents(
        queryDto.questionnaire_id,
        queryDto.grade_code,
        queryDto.class_code,
        queryDto.page || 1,
        queryDto.pageSize || 20
      );

      return {
        errCode: 0,
        msg: '获取未填写学生列表成功',
        data: result,
      };
    } catch (error) {
      this.ctx.logger.error('获取缓存未填写学生列表失败', {
        questionnaire_id: queryDto.questionnaire_id,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '获取未填写学生列表失败',
        data: null,
      };
    }
  }
}
