import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';

/**
 * 简化版统计任务服务
 * 用于测试基本功能，避免复杂的数据库依赖
 */
@Provide()
export class StatisticsTaskSimpleService {
  @Inject()
  ctx: Context;

  /**
   * 触发问卷统计计算
   * @param questionnaireId 问卷ID
   * @param triggeredBy 触发用户ID
   * @returns 统计任务信息
   */
  async triggerStatisticsCalculation(
    questionnaireId: number,
    triggeredBy: string
  ): Promise<any> {
    console.log('触发统计计算', {
      questionnaire_id: questionnaireId,
      triggered_by: triggeredBy,
    });

    // 模拟统计任务
    return {
      id: Date.now(),
      questionnaire_id: questionnaireId,
      status: 'pending',
      triggered_by: triggeredBy,
      created_at: new Date(),
    };
  }

  /**
   * 获取统计状态
   * @param questionnaireId 问卷ID
   * @returns 统计状态信息
   */
  async getStatisticsStatus(questionnaireId: number): Promise<any> {
    console.log('获取统计状态', {
      questionnaire_id: questionnaireId,
    });

    // 模拟状态返回
    return {
      id: Date.now(),
      questionnaire_id: questionnaireId,
      status: 'completed',
      last_calculated_at: new Date(),
      calculation_duration: 1000,
      total_students: 100,
      submitted_count: 80,
      completion_rate: 80.0,
    };
  }

  /**
   * 获取缓存的统计数据
   * @param questionnaireId 问卷ID
   * @returns 缓存的统计数据
   */
  async getCachedStatistics(questionnaireId: number): Promise<any> {
    console.log('获取缓存统计数据', {
      questionnaire_id: questionnaireId,
    });

    // 模拟缓存数据
    return {
      statistics_id: Date.now(),
      questionnaire_id: questionnaireId,
      total_students: 100,
      submitted_count: 80,
      incomplete_count: 20,
      completion_rate: 80.0,
      school_average_score: 85.5,
      teacher_average_score: 87.2,
      total_teachers: 25,
      grade_statistics: {
        '4': {
          grade_name: '四年级',
          total_students: 30,
          submitted_count: 25,
          completion_rate: 83.33,
        },
      },
      class_statistics: {
        '4-5': {
          grade_name: '四年级',
          class_name: '5班',
          total_students: 30,
          submitted_count: 25,
          completion_rate: 83.33,
        },
      },
      teacher_ranking: [
        {
          teacher_id: 'T001',
          teacher_name: '张老师',
          average_score: 95.5,
          evaluation_count: 120,
        },
      ],
      last_calculated_at: new Date(),
      calculation_duration: 1000,
    };
  }

  /**
   * 获取缓存的未填写学生列表
   * @param questionnaireId 问卷ID
   * @param gradeCode 年级编码
   * @param classCode 班级编码
   * @param page 页码
   * @param pageSize 每页数量
   * @returns 未填写学生列表
   */
  async getCachedIncompleteStudents(
    questionnaireId: number,
    gradeCode?: string,
    classCode?: string,
    page = 1,
    pageSize = 20
  ): Promise<any> {
    console.log('获取缓存未填写学生列表', {
      questionnaire_id: questionnaireId,
      grade_code: gradeCode,
      class_code: classCode,
      page,
      pageSize,
    });

    // 模拟未填写学生数据
    const mockStudents = [
      {
        sso_student_code: 'S001',
        sso_student_name: '李小明',
        grade_code: '4',
        grade_name: '四年级',
        class_code: '5',
        class_name: '5班',
        student_mobile: '13800138001',
        parent_contacts: [
          {
            name: '李父',
            phone: '13800138002',
            relation: '父亲',
          },
        ],
        student_status: '在读',
      },
      {
        sso_student_code: 'S002',
        sso_student_name: '王小红',
        grade_code: '4',
        grade_name: '四年级',
        class_code: '5',
        class_name: '5班',
        student_mobile: '13800138003',
        parent_contacts: [
          {
            name: '王母',
            phone: '13800138004',
            relation: '母亲',
          },
        ],
        student_status: '在读',
      },
    ];

    // 应用筛选条件
    let filteredStudents = mockStudents;
    if (gradeCode) {
      filteredStudents = filteredStudents.filter(
        s => s.grade_code === gradeCode
      );
    }
    if (classCode) {
      filteredStudents = filteredStudents.filter(
        s => s.class_code === classCode
      );
    }

    // 分页
    const total = filteredStudents.length;
    const offset = (page - 1) * pageSize;
    const list = filteredStudents.slice(offset, offset + pageSize);
    const totalPages = Math.ceil(total / pageSize);

    return {
      status: 'completed',
      last_calculated_at: new Date(),
      list,
      total,
      page,
      pageSize,
      totalPages,
    };
  }
}
